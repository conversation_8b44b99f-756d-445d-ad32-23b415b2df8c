# Development Environment Configuration for VoiceHealth AI

# Supabase Configuration (Development)
VITE_SUPABASE_URL=https://vbjxfrfwdbebrwdqaqne.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HXFPe8ve1WcW3R-ZvlwQC7u-7NN1EUydR83yHbJ_Z48
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.d9YLdO2fPMxKBBmCIzrIZ8sZ8YDfDzIk5lK3C4Bc7eI

# Payment Configuration (Development - Test Keys)
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your_test_key
# SECURITY: Secret key moved to secure backend API
# PAYSTACK_SECRET_KEY=sk_test_your_test_secret  # REMOVED FOR SECURITY

# SendPulse Email API Configuration (Development)
SENDPULSE_API_USER_ID=84aba612098094dbbad523f2a04c1c08
SENDPULSE_API_SECRET=264e665c62e272b1114b8d50acb881b7
SENDPULSE_API_TOKEN_STORAGE=/tmp/

# AI Services Configuration (Development)
# Client-side keys (VITE_ prefix required for browser access)
VITE_OPENAI_API_KEY=********************************************************************************************************************************************************************
VITE_ANTHROPIC_API_KEY=************************************************************************************************************
VITE_ELEVENLABS_API_KEY=***************************************************
VITE_GEMINI_API_KEY=AIzaSyAr39e5fpsmiI3nBksiN0q6PC1d-FO9hxs

# Server-side keys (for backend/webhook processing)
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
ELEVENLABS_API_KEY=***************************************************


# Application Configuration
VITE_NODE_ENV=development
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_URL=http://localhost:4028

# Development Features
VITE_ENABLE_ANALYTICS=false
VITE_ERROR_REPORTING=false
VITE_PERFORMANCE_MONITORING=true

# PWA Configuration
VITE_PWA_ENABLED=true
VITE_OFFLINE_SUPPORT=true
VITE_PUSH_NOTIFICATIONS=false
