{"name": "voicehealth_ai", "version": "0.1.0", "type": "module", "private": true, "dependencies": {"@dhiwise/component-tagger": "^1.0.9", "@reduxjs/toolkit": "^2.6.1", "@supabase/supabase-js": "^2.39.0", "@tailwindcss/forms": "^0.5.7", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "axios": "^1.8.4", "d3": "^7.9.0", "date-fns": "^4.1.0", "dexie": "^4.0.11", "dotenv": "^16.0.1", "framer-motion": "^10.16.4", "lucide-react": "^0.484.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-router-dom": "6.0.2", "react-router-hash-link": "^2.4.3", "recharts": "^2.15.2", "redux": "^5.0.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-elevation": "^2.0.0", "tailwindcss-fluid-type": "^2.0.7"}, "scripts": {"start": "vite", "dev": "vite --mode development", "build": "vite build --sourcemap", "build:prod": "vite build --mode production --sourcemap=hidden", "build:staging": "vite build --mode staging --sourcemap", "serve": "vite preview", "preview:prod": "vite preview --mode production --port 4029", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "lint": "eslint src --ext ts,tsx,js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx,js,jsx --fix", "type-check": "tsc --noEmit", "cypress:open": "cypress open", "cypress:run": "cypress run", "lighthouse": "lhci autorun", "analyze": "npx vite-bundle-analyzer", "seed-demo": "node scripts/seedDemoData.js seed", "test-demo": "node scripts/seedDemoData.js test", "cleanup-demo": "node scripts/seedDemoData.js cleanup", "docker:build": "docker build -f docker/production.Dockerfile -t voicehealth-ai .", "docker:run": "docker run -p 4028:4028 voicehealth-ai", "health-check": "curl -f http://localhost:4028/api/health || exit 1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/line-clamp": "^0.1.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.2.4", "autoprefixer": "10.4.2", "jsdom": "^26.1.0", "postcss": "8.4.8", "tailwindcss": "3.4.6", "vite": "^5.0.0", "vite-tsconfig-paths": "3.6.0", "vitest": "^3.2.4"}}