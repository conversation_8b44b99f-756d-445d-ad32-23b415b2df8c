import { supabase } from './supabaseClient';
import sendPulseService from './sendPulseService';

const PAYSTACK_PUBLIC_KEY = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY;
const PAYSTACK_SECRET_KEY = import.meta.env.VITE_PAYSTACK_SECRET_KEY;

class PaystackService {
  constructor() {
    if (!PAYSTACK_PUBLIC_KEY) {
      console.warn('Paystack public key not found in environment variables');
    }
  }

  /**
   * Enhanced error handling for database operations
   */
  async handleDatabaseError(error, context = '') {
    const errorInfo = {
      code: error.code,
      message: error.message,
      details: error.details,
      hint: error.hint,
      context
    };

    console.error('PaystackService Database Error:', errorInfo);

    // Handle specific error codes
    if (error.code === '42P01') { // relation does not exist
      return {
        type: 'missing_table',
        message: 'Database table does not exist. Migration required.',
        needsMigration: true,
        fallbackAvailable: context.includes('subscription_plans'),
        error: errorInfo
      };
    }

    if (error.code === 'PGRST204') { // PostgREST table not found
      return {
        type: 'missing_table',
        message: 'Table not found in database schema.',
        needsMigration: true,
        fallbackAvailable: context.includes('subscription_plans'),
        error: errorInfo
      };
    }

    return {
      type: 'unknown_error',
      message: error.message || 'Unknown database error occurred',
      error: errorInfo
    };
  }

  // Initialize Paystack payment
  initializePayment = async (paymentData) => {
    try {
      const response = await fetch('https://api.paystack.co/transaction/initialize', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: paymentData.email,
          amount: Math.round(paymentData.amount * 100), // Convert to kobo
          currency: paymentData.currency || 'NGN',
          reference: paymentData.reference,
          callback_url: paymentData.callback_url,
          metadata: {
            user_id: paymentData.user_id,
            subscription_id: paymentData.subscription_id,
            consultation_session_id: paymentData.consultation_session_id,
            ...paymentData.metadata
          },
          channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
        })
      });

      const result = await response.json();
      
      if (!result.status) {
        return { 
          success: false, 
          error: result.message || 'Failed to initialize payment' 
        };
      }

      // Store transaction in database
      const transactionResult = await this.createTransaction({
        user_id: paymentData.user_id,
        paystack_reference: paymentData.reference,
        amount: paymentData.amount,
        currency: paymentData.currency || 'NGN',
        subscription_id: paymentData.subscription_id,
        consultation_session_id: paymentData.consultation_session_id,
        metadata: paymentData.metadata || {}
      });

      if (!transactionResult.success) {
        console.error('Failed to store transaction:', transactionResult.error);
      }

      return {
        success: true,
        data: {
          authorization_url: result.data.authorization_url,
          access_code: result.data.access_code,
          reference: result.data.reference
        }
      };
    } catch (error) {
      console.error('Paystack initialization error:', error);
      return { 
        success: false, 
        error: 'Network error occurred while initializing payment' 
      };
    }
  };

  // Verify payment
  verifyPayment = async (reference) => {
    try {
      const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();
      
      if (!result.status) {
        return { 
          success: false, 
          error: result.message || 'Payment verification failed' 
        };
      }

      const transactionData = result.data;
      
      // Update transaction in database
      const updateResult = await this.updateTransaction(reference, {
        paystack_transaction_id: transactionData.id.toString(),
        status: transactionData.status === 'success' ? 'completed' : 'failed',
        payment_method: this.mapPaymentChannel(transactionData.channel),
        gateway_response: transactionData,
        payment_date: new Date(transactionData.paid_at).toISOString(),
        verification_date: new Date().toISOString()
      });

      if (!updateResult.success) {
        console.error('Failed to update transaction:', updateResult.error);
      }

      // If payment successful and for subscription, activate subscription
      if (transactionData.status === 'success' && transactionData.metadata?.subscription_id) {
        const activationResult = await this.activateSubscription(
          transactionData.metadata.subscription_id,
          transactionData.metadata.user_id
        );

        // Send payment confirmation email
        if (activationResult.success) {
          try {
            // Get user profile and subscription details
            const { data: userProfile } = await supabase
              .from('user_profiles')
              .select('email, full_name')
              .eq('id', transactionData.metadata.user_id)
              .single();

            const { data: subscription } = await supabase
              .from('user_subscriptions')
              .select(`
                *,
                subscription_plans (
                  name,
                  description,
                  features,
                  duration_days,
                  consultation_credits
                )
              `)
              .eq('id', transactionData.metadata.subscription_id)
              .single();

            if (userProfile?.email) {
              await sendPulseService.sendPaymentConfirmation(
                userProfile.email,
                userProfile.full_name || userProfile.email.split('@')[0],
                {
                  amount: transactionData.amount / 100,
                  payment_method: this.mapPaymentChannel(transactionData.channel),
                  reference: transactionData.reference,
                  date: transactionData.paid_at
                },
                subscription?.subscription_plans
              );
            }
          } catch (emailError) {
            console.warn('Failed to send payment confirmation email:', emailError);
            // Don't fail the payment verification if email fails
          }
        }
      } else if (transactionData.status === 'failed') {
        // Send payment failed email
        try {
          const { data: userProfile } = await supabase
            .from('user_profiles')
            .select('email, full_name')
            .eq('id', transactionData.metadata.user_id)
            .single();

          if (userProfile?.email) {
            await sendPulseService.sendPaymentFailed(
              userProfile.email,
              userProfile.full_name || userProfile.email.split('@')[0],
              {
                amount: transactionData.amount / 100,
                reference: transactionData.reference
              }
            );
          }
        } catch (emailError) {
          console.warn('Failed to send payment failed email:', emailError);
        }
      }

      return {
        success: true,
        data: {
          status: transactionData.status,
          amount: transactionData.amount / 100, // Convert from kobo
          currency: transactionData.currency,
          paid_at: transactionData.paid_at,
          channel: transactionData.channel,
          reference: transactionData.reference,
          metadata: transactionData.metadata
        }
      };
    } catch (error) {
      console.error('Payment verification error:', error);
      return { 
        success: false, 
        error: 'Network error occurred while verifying payment' 
      };
    }
  };

  // Create transaction record
  createTransaction = async (transactionData) => {
    try {
      const { data, error } = await supabase
        .from('payment_transactions')
        .insert(transactionData)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: 'Failed to create transaction record' 
      };
    }
  };

  // Update transaction record
  updateTransaction = async (reference, updates) => {
    try {
      const { data, error } = await supabase
        .from('payment_transactions')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('paystack_reference', reference)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: 'Failed to update transaction record' 
      };
    }
  };

  // Get user's payment history
  getUserTransactions = async (userId) => {
    try {
      const { data, error } = await supabase
        .from('payment_transactions')
        .select(`
          *,
          subscription_plans!user_subscriptions_plan_id_fkey (
            name,
            description
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: 'Failed to fetch payment history' 
      };
    }
  };

  // Activate subscription
  activateSubscription = async (subscriptionId, userId) => {
    try {
      // Get subscription plan details
      const { data: subscription, error: subError } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (
            duration_days,
            consultation_credits
          )
        `)
        .eq('id', subscriptionId)
        .single();

      if (subError) {
        return { success: false, error: subError.message };
      }

      // Calculate expiry date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + subscription.subscription_plans.duration_days);

      // Update subscription status
      const { data, error } = await supabase
        .from('user_subscriptions')
        .update({
          status: 'active',
          started_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString(),
          consultation_credits_remaining: subscription.subscription_plans.consultation_credits,
          updated_at: new Date().toISOString()
        })
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: 'Failed to activate subscription' 
      };
    }
  };

  // Get subscription plans with enhanced error handling and smart fallback
  getSubscriptionPlans = async () => {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_ngn', { ascending: true });

      if (error) {
        console.error('Database error:', error);
        const handledError = await this.handleDatabaseError(error, 'getSubscriptionPlans:subscription_plans');
        
        // If table doesn't exist, return fallback plans with detailed error info
        if (handledError.type === 'missing_table') {
          console.warn('subscription_plans table not found, using fallback plans');
          return { 
            success: true, 
            data: this.getFallbackSubscriptionPlans(),
            fallback: true,
            error: handledError.message,
            errorDetails: handledError,
            recommendation: 'Run database migration to create subscription_plans table'
          };
        }
        
        return { 
          success: false, 
          error: handledError.message,
          errorDetails: handledError
        };
      }

      // If no plans found, use fallback
      if (!data || data.length === 0) {
        console.warn('No subscription plans found in database, using fallback plans');
        return { 
          success: true, 
          data: this.getFallbackSubscriptionPlans(),
          fallback: true,
          warning: 'Database table exists but contains no active plans'
        };
      }

      return { success: true, data };
    } catch (error) {
      console.error('Subscription plans fetch error:', error);
      const handledError = await this.handleDatabaseError(error, 'getSubscriptionPlans:catch');
      return { 
        success: true, 
        data: this.getFallbackSubscriptionPlans(),
        fallback: true,
        error: 'Using fallback plans due to database error',
        errorDetails: handledError
      };
    }
  };

  // Enhanced fallback subscription plans with consistent structure
  getFallbackSubscriptionPlans = () => {
    return [
      {
        id: 'basic-plan-demo',
        name: 'Basic Plan',
        description: 'Perfect for occasional consultations',
        price_ngn: 5000.00,
        price_usd: 12.00,
        duration_days: 30,
        consultation_credits: 3,
        features: [
          '3 consultations per month',
          'Basic AI agents',
          'Text transcripts',
          'Email support'
        ],
        is_active: true,
        is_popular: false,
        demo: true // Flag to indicate this is demo data
      },
      {
        id: 'premium-plan-demo',
        name: 'Premium Plan',
        description: 'Most popular for regular health monitoring',
        price_ngn: 12000.00,
        price_usd: 28.00,
        duration_days: 30,
        consultation_credits: 10,
        features: [
          '10 consultations per month',
          'All AI specialists',
          'Audio recordings',
          'Priority support',
          'Health insights'
        ],
        is_active: true,
        is_popular: true,
        demo: true
      },
      {
        id: 'professional-plan-demo',
        name: 'Professional Plan',
        description: 'Unlimited access for comprehensive care',
        price_ngn: 25000.00,
        price_usd: 60.00,
        duration_days: 30,
        consultation_credits: 0, // 0 means unlimited
        features: [
          'Unlimited consultations',
          'All premium features',
          'Personal health dashboard',
          '24/7 priority support',
          'Advanced analytics'
        ],
        is_active: true,
        is_popular: false,
        demo: true
      },
      {
        id: 'annual-basic-demo',
        name: 'Annual Basic',
        description: 'Basic plan with annual discount',
        price_ngn: 50000.00,
        price_usd: 120.00,
        duration_days: 365,
        consultation_credits: 36,
        features: [
          '3 consultations per month',
          'Basic AI agents',
          'Text transcripts',
          'Email support',
          '2 months free'
        ],
        is_active: true,
        is_popular: false,
        demo: true
      },
      {
        id: 'annual-premium-demo',
        name: 'Annual Premium',
        description: 'Premium plan with annual discount',
        price_ngn: 120000.00,
        price_usd: 280.00,
        duration_days: 365,
        consultation_credits: 120,
        features: [
          '10 consultations per month',
          'All AI specialists',
          'Audio recordings',
          'Priority support',
          'Health insights',
          '2 months free'
        ],
        is_active: true,
        is_popular: false,
        demo: true
      }
    ];
  };

  // Enhanced getUserActiveSubscription with better error handling
  getUserActiveSubscription = async (userId) => {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (
            name,
            description,
            features,
            consultation_credits
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .gt('expires_at', new Date().toISOString())
        .order('expires_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        const handledError = await this.handleDatabaseError(error, 'getUserActiveSubscription');
        return { 
          success: false, 
          error: handledError.message,
          errorDetails: handledError
        };
      }

      return { success: true, data: data || null };
    } catch (error) {
      const handledError = await this.handleDatabaseError(error, 'getUserActiveSubscription:catch');
      return { 
        success: false, 
        error: 'Failed to fetch active subscription',
        errorDetails: handledError
      };
    }
  };

  // Enhanced createSubscription with fallback for demo plans
  createSubscription = async (userId, planId) => {
    try {
      // Check if this is a demo plan (fallback plan)
      if (planId.includes('demo')) {
        console.warn(`Demo plan subscription attempted: ${planId}. Database migration may be required.`);
        return { 
          success: false, 
          error: 'Cannot create subscription for demo plan. Please set up the database first.',
          needsDatabaseSetup: true
        };
      }

      const { data, error } = await supabase
        .from('user_subscriptions')
        .insert({
          user_id: userId,
          plan_id: planId,
          status: 'inactive', // Will be activated after payment
          expires_at: new Date().toISOString() // Temporary, will be updated after payment
        })
        .select()
        .single();

      if (error) {
        const handledError = await this.handleDatabaseError(error, 'createSubscription');
        return { 
          success: false, 
          error: handledError.message,
          errorDetails: handledError
        };
      }

      return { success: true, data };
    } catch (error) {
      const handledError = await this.handleDatabaseError(error, 'createSubscription:catch');
      return { 
        success: false, 
        error: 'Failed to create subscription',
        errorDetails: handledError
      };
    }
  };

  // Generate payment reference
  generateReference = (prefix = 'vh') => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `${prefix}_${timestamp}_${random}`;
  };

  // Map Paystack payment channel to our enum
  mapPaymentChannel = (channel) => {
    const channelMap = {
      'card': 'card',
      'bank': 'bank',
      'ussd': 'ussd',
      'qr': 'qr',
      'mobile_money': 'bank',
      'bank_transfer': 'bank_transfer'
    };
    return channelMap[channel] || 'card';
  };

  // Open Paystack popup (frontend integration)
  openPaystackPopup = (paymentData) => {
    if (typeof window === 'undefined' || !window.PaystackPop) {
      return {
        success: false,
        error: 'Paystack popup not available. Please ensure Paystack script is loaded.'
      };
    }

    try {
      const handler = window.PaystackPop.setup({
        key: PAYSTACK_PUBLIC_KEY,
        email: paymentData.email,
        amount: Math.round(paymentData.amount * 100), // Convert to kobo
        currency: paymentData.currency || 'NGN',
        ref: paymentData.reference,
        metadata: paymentData.metadata || {},
        onClose: paymentData.onClose || function() {
          console.log('Payment window closed.');
        },
        callback: paymentData.callback || function(response) {
          console.log('Payment complete! Reference: ' + response.reference);
        }
      });

      handler.openIframe();
      return { success: true };
    } catch (error) {
      console.error('Paystack popup error:', error);
      return {
        success: false,
        error: 'Failed to open payment popup'
      };
    }
  };

  // Check if user can start consultation (has credits or subscription)
  canStartConsultation = async (userId) => {
    try {
      const subscriptionResult = await this.getUserActiveSubscription(userId);
      
      if (!subscriptionResult.success) {
        return { success: false, error: subscriptionResult.error };
      }

      const subscription = subscriptionResult.data;
      
      // If no active subscription
      if (!subscription) {
        return { 
          success: true, 
          canStart: false, 
          reason: 'no_subscription' 
        };
      }

      // If unlimited consultations (credits = 0)
      if (subscription.subscription_plans.consultation_credits === 0) {
        return { 
          success: true, 
          canStart: true, 
          subscription 
        };
      }

      // Check if has remaining credits
      if (subscription.consultation_credits_remaining > 0) {
        return { 
          success: true, 
          canStart: true, 
          credits: subscription.consultation_credits_remaining,
          subscription 
        };
      }

      return { 
        success: true, 
        canStart: false, 
        reason: 'no_credits',
        subscription 
      };
    } catch (error) {
      return { 
        success: false, 
        error: 'Failed to check consultation eligibility' 
      };
    }
  };
}

const paystackService = new PaystackService();
export default paystackService;