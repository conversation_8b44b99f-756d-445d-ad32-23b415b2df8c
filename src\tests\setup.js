/**
 * Test Setup Configuration
 * Global test setup for PWA and React components
 */

import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';

// Cleanup after each test
afterEach(() => {
  cleanup();
});

// Mock global APIs that are not available in jsdom
beforeAll(() => {
  // Mock Service Worker API
  Object.defineProperty(window.navigator, 'serviceWorker', {
    value: {
      register: vi.fn(() => Promise.resolve({
        installing: null,
        waiting: null,
        active: {
          scriptURL: '/sw.js',
          state: 'activated'
        },
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      })),
      ready: Promise.resolve({
        installing: null,
        waiting: null,
        active: {
          scriptURL: '/sw.js',
          state: 'activated'
        },
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      }),
      controller: null,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    },
    writable: true
  });

  // Mock IndexedDB
  const mockIDBKeyRange = {
    only: vi.fn(),
    lowerBound: vi.fn(),
    upperBound: vi.fn(),
    bound: vi.fn()
  };

  const mockTransaction = {
    objectStore: vi.fn(() => ({
      get: vi.fn(() => ({ onsuccess: null, result: {} })),
      put: vi.fn(() => ({ onsuccess: null })),
      delete: vi.fn(() => ({ onsuccess: null })),
      getAll: vi.fn(() => ({ onsuccess: null, result: [] })),
      clear: vi.fn(() => ({ onsuccess: null }))
    })),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
  };

  const mockDatabase = {
    createObjectStore: vi.fn(),
    deleteObjectStore: vi.fn(),
    transaction: vi.fn(() => mockTransaction),
    close: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
  };

  const mockOpenRequest = {
    onsuccess: null,
    onerror: null,
    onupgradeneeded: null,
    result: mockDatabase,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
  };

  global.IDBKeyRange = mockIDBKeyRange;
  global.indexedDB = {
    open: vi.fn(() => mockOpenRequest),
    deleteDatabase: vi.fn(() => mockOpenRequest),
    databases: vi.fn(() => Promise.resolve([]))
  };

  // Mock Storage API
  Object.defineProperty(window.navigator, 'storage', {
    value: {
      estimate: vi.fn(() => Promise.resolve({
        quota: 1024 * 1024 * 1024, // 1GB
        usage: 1024 * 1024 * 50     // 50MB
      })),
      persist: vi.fn(() => Promise.resolve(true)),
      persisted: vi.fn(() => Promise.resolve(false))
    },
    writable: true
  });

  // Mock Web Speech API
  global.SpeechRecognition = vi.fn(() => ({
    start: vi.fn(),
    stop: vi.fn(),
    abort: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    continuous: false,
    interimResults: false,
    lang: 'en-US'
  }));

  global.webkitSpeechRecognition = global.SpeechRecognition;

  // Mock MediaRecorder
  global.MediaRecorder = vi.fn(() => ({
    start: vi.fn(),
    stop: vi.fn(),
    pause: vi.fn(),
    resume: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    state: 'inactive',
    stream: null,
    mimeType: 'audio/webm'
  }));

  // Mock getUserMedia
  Object.defineProperty(window.navigator, 'mediaDevices', {
    value: {
      getUserMedia: vi.fn(() => Promise.resolve({
        getTracks: () => [{
          stop: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn()
        }]
      })),
      enumerateDevices: vi.fn(() => Promise.resolve([]))
    },
    writable: true
  });

  // Mock Performance API
  global.performance = {
    ...global.performance,
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn()
  };

  // Mock PerformanceObserver
  global.PerformanceObserver = vi.fn(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn(() => [])
  }));

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));

  // Mock fetch for API calls
  global.fetch = vi.fn(() => 
    Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
      blob: () => Promise.resolve(new Blob()),
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
    })
  );

  // Mock WebSocket
  global.WebSocket = vi.fn(() => ({
    close: vi.fn(),
    send: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    readyState: 1, // OPEN
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
  }));

  // Mock window.location
  delete window.location;
  window.location = {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn()
  };

  // Mock console methods to reduce test noise
  global.console = {
    ...console,
    log: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  };

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn((key) => localStorageMock.store[key] || null),
    setItem: vi.fn((key, value) => {
      localStorageMock.store[key] = String(value);
    }),
    removeItem: vi.fn((key) => {
      delete localStorageMock.store[key];
    }),
    clear: vi.fn(() => {
      localStorageMock.store = {};
    }),
    key: vi.fn((index) => Object.keys(localStorageMock.store)[index] || null),
    get length() {
      return Object.keys(localStorageMock.store).length;
    },
    store: {}
  };

  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true
  });

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn((key) => sessionStorageMock.store[key] || null),
    setItem: vi.fn((key, value) => {
      sessionStorageMock.store[key] = String(value);
    }),
    removeItem: vi.fn((key) => {
      delete sessionStorageMock.store[key];
    }),
    clear: vi.fn(() => {
      sessionStorageMock.store = {};
    }),
    key: vi.fn((index) => Object.keys(sessionStorageMock.store)[index] || null),
    get length() {
      return Object.keys(sessionStorageMock.store).length;
    },
    store: {}
  };

  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
    writable: true
  });

  // Mock AudioContext
  global.AudioContext = vi.fn(() => ({
    createAnalyser: vi.fn(() => ({
      connect: vi.fn(),
      disconnect: vi.fn(),
      getByteFrequencyData: vi.fn(),
      getByteTimeDomainData: vi.fn()
    })),
    createGain: vi.fn(() => ({
      connect: vi.fn(),
      disconnect: vi.fn(),
      gain: { value: 1 }
    })),
    createMediaStreamSource: vi.fn(() => ({
      connect: vi.fn(),
      disconnect: vi.fn()
    })),
    close: vi.fn(() => Promise.resolve()),
    resume: vi.fn(() => Promise.resolve()),
    suspend: vi.fn(() => Promise.resolve()),
    state: 'running',
    sampleRate: 44100
  }));

  global.webkitAudioContext = global.AudioContext;
});

// Reset mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
  
  // Reset localStorage
  window.localStorage.clear();
  window.sessionStorage.clear();
  
  // Reset online status
  Object.defineProperty(window.navigator, 'onLine', {
    value: true,
    writable: true
  });
});

// Cleanup after all tests
afterAll(() => {
  vi.restoreAllMocks();
});

// Helper function to simulate network events
export const simulateNetworkChange = (online) => {
  Object.defineProperty(window.navigator, 'onLine', {
    value: online,
    writable: true
  });
  
  const event = new Event(online ? 'online' : 'offline');
  window.dispatchEvent(event);
};

// Helper function to simulate service worker events
export const simulateServiceWorkerEvent = (type, data = {}) => {
  const event = new MessageEvent('message', {
    data: { type, ...data },
    source: null,
    origin: window.location.origin
  });
  
  if (window.navigator.serviceWorker) {
    window.navigator.serviceWorker.dispatchEvent(event);
  }
};

// Helper to wait for async operations
export const waitFor = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));

// Mock audio blob for voice testing
export const createMockAudioBlob = (size = 1024) => {
  const buffer = new ArrayBuffer(size);
  return new Blob([buffer], { type: 'audio/wav' });
};

// Mock audio chunk for voice persistence testing
export const createMockAudioChunk = (index = 0, duration = 1000) => {
  const buffer = new ArrayBuffer(4096);
  return {
    data: buffer,
    metadata: {
      chunkIndex: index,
      timestamp: Date.now(),
      duration,
      quality: 'high'
    }
  };
};
